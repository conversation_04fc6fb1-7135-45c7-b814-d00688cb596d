# 组件优化指南

## 问题解决方案

### 1. 权限配置优化
已修复 `app.json` 中的权限配置，使用更规范的描述文本：
- `scope.userLocation`: 获取位置信息用于基于位置的服务
- `scope.camera`: 访问相机用于拍照和扫码功能  
- `scope.album`: 访问相册用于图片选择和上传

### 2. WXSS 语法优化
已优化 `app.wxss` 中的通用选择器使用：
- 将 `*` 选择器替换为具体的小程序组件选择器
- 减少了样式计算开销，提升渲染性能
- 保持了相同的功能效果

### 3. 全局组件优化
已将全局组件从 9 个减少到 3 个核心组件：
- `t-icon`: 图标组件（使用频率最高）
- `t-loading`: 加载组件（全局状态必需）
- `t-toast`: 提示组件（全局反馈必需）

其他组件改为按需引入，配合 `LazyCodeLoading` 功能。

## 使用方法

### 页面级组件引入
在各页面的 `.json` 文件中按需引入组件：

```json
{
  "usingComponents": {
    "t-button": "tdesign-miniprogram/button/button",
    "t-cell": "tdesign-miniprogram/cell/cell",
    "t-input": "tdesign-miniprogram/input/input"
  }
}
```

### 使用组件配置工具
可以使用 `utils/component-config.js` 中的配置：

```javascript
import { getPageComponents } from '../../utils/component-config.js';

// 在页面 JS 中获取组件配置
const components = getPageComponents('pages/home/<USER>');
```

## 性能优化效果

1. **包体积减少**: 全局组件减少 66%，预计减少首屏加载时间
2. **按需加载**: 配合 LazyCodeLoading，只加载当前页面需要的组件
3. **渲染性能**: 优化 CSS 选择器，减少样式计算开销

## 注意事项

1. 新增页面时，记得在对应的 `.json` 文件中引入所需组件
2. 如果页面使用了新的 TDesign 组件，需要在页面级配置中添加
3. 全局组件（icon、loading、toast）可以在任何页面直接使用
4. 建议定期检查组件使用情况，进一步优化配置

## 故障排除

### 微信开发者工具相关问题
1. **Babel runtime 文件缺失**: 
   - 重启微信开发者工具
   - 清除缓存后重新编译
   - 更新到最新版本的开发者工具

2. **WXSS 编译错误**:
   - 检查是否有语法错误
   - 确认 CSS 属性是否被小程序支持
   - 尝试重新编译项目

3. **权限配置警告**:
   - 确认权限描述文本符合微信规范
   - 检查是否有未使用的权限配置
   - 在真机上测试权限申请流程
