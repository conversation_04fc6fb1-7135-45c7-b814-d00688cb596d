{"name": "smart-goose-saas", "version": "3.0.0", "description": "智慧养鹅微信小程序 - 专业的养鹅管理系统", "main": "app.js", "scripts": {"dev": "npm run dev:miniprogram", "dev:miniprogram": "echo '开发环境启动'", "build": "npm run build:prod", "build:prod": "npm run lint && npm run test", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "test": "npm run test:unit && npm run test:e2e", "test:unit": "node test/unit/index.js", "test:e2e": "node test/e2e/index.js", "test:performance": "node test/performance/index.js", "clean": "rm -rf dist/ && rm -rf node_modules/.cache", "analyze": "npm run build && node scripts/analyze-bundle.js", "deploy": "npm run build && npm run upload", "upload": "echo '上传到微信开发者工具'", "preview": "echo '预览小程序'", "backup": "node scripts/backup-project.js", "docs": "node scripts/generate-docs.js", "setup-tdesign": "node scripts/setup-tdesign.js"}, "keywords": ["miniprogram", "wechat", "goose", "farming", "management", "saas"], "author": "Smart Goose Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/smart-goose/miniprogram.git"}, "bugs": {"url": "https://github.com/smart-goose/miniprogram/issues"}, "homepage": "https://github.com/smart-goose/miniprogram#readme", "devDependencies": {"eslint": "^8.0.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.25.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "miniprogram-api-typings": "^3.8.0", "typescript": "^4.9.0"}, "dependencies": {"tdesign-miniprogram": "^1.0.0"}, "miniprogram": {"components": ["components/base/**", "components/business/**", "components/layout/**", "components/performance/**"], "pages": ["pages/**"], "subpackages": ["subpackages/**"]}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "babel": {"presets": [["@babel/preset-env", {"targets": {"browsers": ["last 2 versions", "iOS >= 8", "Android >= 4.4"]}}]], "plugins": ["@babel/plugin-proposal-object-rest-spread", "@babel/plugin-transform-runtime"]}}