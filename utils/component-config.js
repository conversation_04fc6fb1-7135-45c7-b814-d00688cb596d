/**
 * TDesign 组件按需引入配置
 * 用于优化小程序性能，配合 LazyCodeLoading 功能
 */

// 基础组件配置 - 在各页面按需引入
export const BASIC_COMPONENTS = {
  't-button': 'tdesign-miniprogram/button/button',
  't-image': 'tdesign-miniprogram/image/image',
  't-dialog': 'tdesign-miniprogram/dialog/dialog'
};

// 导航组件配置
export const NAVIGATION_COMPONENTS = {
  't-navbar': 'tdesign-miniprogram/navbar/navbar',
  't-tabs': 'tdesign-miniprogram/tabs/tabs',
  't-tab-panel': 'tdesign-miniprogram/tab-panel/tab-panel'
};

// 表单组件配置
export const FORM_COMPONENTS = {
  't-input': 'tdesign-miniprogram/input/input',
  't-textarea': 'tdesign-miniprogram/textarea/textarea',
  't-picker': 'tdesign-miniprogram/picker/picker',
  't-switch': 'tdesign-miniprogram/switch/switch',
  't-checkbox': 'tdesign-miniprogram/checkbox/checkbox',
  't-radio': 'tdesign-miniprogram/radio/radio'
};

// 数据展示组件配置
export const DATA_COMPONENTS = {
  't-cell': 'tdesign-miniprogram/cell/cell',
  't-cell-group': 'tdesign-miniprogram/cell-group/cell-group',
  't-tag': 'tdesign-miniprogram/tag/tag',
  't-badge': 'tdesign-miniprogram/badge/badge',
  't-progress': 'tdesign-miniprogram/progress/progress'
};

// 反馈组件配置
export const FEEDBACK_COMPONENTS = {
  't-message': 'tdesign-miniprogram/message/message',
  't-notice-bar': 'tdesign-miniprogram/notice-bar/notice-bar',
  't-skeleton': 'tdesign-miniprogram/skeleton/skeleton'
};

// 页面级组件配置映射
export const PAGE_COMPONENT_MAP = {
  // 首页组件
  'pages/home/<USER>': {
    ...BASIC_COMPONENTS,
    ...NAVIGATION_COMPONENTS,
    ...DATA_COMPONENTS
  },
  
  // 健康模块组件
  'pages/health/health': {
    ...BASIC_COMPONENTS,
    ...NAVIGATION_COMPONENTS,
    ...DATA_COMPONENTS,
    ...FORM_COMPONENTS
  },
  
  // 生产模块组件
  'pages/production/production': {
    ...BASIC_COMPONENTS,
    ...NAVIGATION_COMPONENTS,
    ...DATA_COMPONENTS,
    ...FORM_COMPONENTS
  },
  
  // 商城模块组件
  'pages/shop/shop': {
    ...BASIC_COMPONENTS,
    ...DATA_COMPONENTS,
    't-stepper': 'tdesign-miniprogram/stepper/stepper'
  },
  
  // OA模块组件
  'pages/oa/oa': {
    ...BASIC_COMPONENTS,
    ...NAVIGATION_COMPONENTS,
    ...FORM_COMPONENTS,
    ...FEEDBACK_COMPONENTS
  },
  
  // 个人中心组件
  'pages/profile/profile': {
    ...BASIC_COMPONENTS,
    ...DATA_COMPONENTS
  }
};

/**
 * 获取页面所需的组件配置
 * @param {string} pagePath 页面路径
 * @returns {object} 组件配置对象
 */
export function getPageComponents(pagePath) {
  return PAGE_COMPONENT_MAP[pagePath] || BASIC_COMPONENTS;
}

/**
 * 生成页面 JSON 配置中的 usingComponents 字段
 * @param {string} pagePath 页面路径
 * @returns {object} usingComponents 配置
 */
export function generateUsingComponents(pagePath) {
  const components = getPageComponents(pagePath);
  return {
    usingComponents: components
  };
}
