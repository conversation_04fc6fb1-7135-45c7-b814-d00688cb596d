/**
 * 修复微信开发者工具常见问题的脚本
 * 解决 Babel runtime 文件缺失等问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复微信开发者工具问题...\n');

// 1. 检查并创建必要的目录结构
function ensureDirectories() {
  const dirs = [
    'node_modules/@babel/runtime/helpers',
    'miniprogram_npm/@babel/runtime/helpers',
    '.cache',
    'dist'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ 创建目录: ${dir}`);
    }
  });
}

// 2. 创建缺失的 Babel runtime 文件
function createBabelRuntimeFiles() {
  const definePropertyContent = `
module.exports = function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
};
`;

  const files = [
    'node_modules/@babel/runtime/helpers/defineProperty.js',
    'miniprogram_npm/@babel/runtime/helpers/defineProperty.js'
  ];
  
  files.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      fs.writeFileSync(filePath, definePropertyContent);
      console.log(`✅ 创建文件: ${filePath}`);
    }
  });
}

// 3. 创建项目配置文件
function createProjectConfig() {
  const projectConfig = {
    "description": "项目配置文件",
    "packOptions": {
      "ignore": [
        {
          "type": "file",
          "value": ".eslintrc.js"
        },
        {
          "type": "file", 
          "value": ".gitignore"
        },
        {
          "type": "file",
          "value": "README.md"
        },
        {
          "type": "folder",
          "value": "docs"
        },
        {
          "type": "folder",
          "value": "test"
        }
      ]
    },
    "setting": {
      "urlCheck": false,
      "es6": true,
      "enhance": true,
      "postcss": true,
      "preloadBackgroundData": false,
      "minified": true,
      "newFeature": true,
      "coverView": true,
      "nodeModules": true,
      "autoAudits": false,
      "showShadowRootInWxmlPanel": true,
      "scopeDataCheck": false,
      "uglifyFileName": false,
      "checkInvalidKey": true,
      "checkSiteMap": true,
      "uploadWithSourceMap": true,
      "compileHotReLoad": false,
      "lazyloadPlaceholderEnable": false,
      "useMultiFrameRuntime": true,
      "useApiHook": true,
      "useApiHostProcess": true,
      "babelSetting": {
        "ignore": [],
        "disablePlugins": [],
        "outputPath": ""
      },
      "enableEngineNative": false,
      "useIsolateContext": true,
      "userConfirmedBundleSwitch": false,
      "packNpmManually": false,
      "packNpmRelationList": [],
      "minifyWXSS": true,
      "disableUseStrict": false,
      "minifyWXML": true,
      "showES6CompileOption": false,
      "useCompilerPlugins": false,
      "ignoreUploadUnusedFiles": true
    },
    "compileType": "miniprogram",
    "libVersion": "3.8.12",
    "appid": "your-app-id",
    "projectname": "智慧养鹅",
    "debugOptions": {
      "hidedInDevtools": []
    },
    "scripts": {},
    "staticServerOptions": {
      "baseURL": "",
      "servePath": ""
    },
    "isGameTourist": false,
    "condition": {
      "search": {
        "list": []
      },
      "conversation": {
        "list": []
      },
      "game": {
        "list": []
      },
      "plugin": {
        "list": []
      },
      "gamePlugin": {
        "list": []
      },
      "miniprogram": {
        "list": []
      }
    }
  };
  
  if (!fs.existsSync('project.config.json')) {
    fs.writeFileSync('project.config.json', JSON.stringify(projectConfig, null, 2));
    console.log('✅ 创建 project.config.json');
  }
}

// 4. 清理缓存
function clearCache() {
  const cacheDirs = ['.cache', 'node_modules/.cache'];
  
  cacheDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`🗑️  清理缓存: ${dir}`);
    }
  });
}

// 5. 输出修复建议
function outputSuggestions() {
  console.log('\n📋 修复完成！请按以下步骤操作：\n');
  console.log('1. 重启微信开发者工具');
  console.log('2. 点击"工具" -> "构建 npm"');
  console.log('3. 如果仍有问题，请尝试：');
  console.log('   - 清除缓存并重新编译');
  console.log('   - 更新微信开发者工具到最新版本');
  console.log('   - 检查 TDesign 组件库版本是否兼容');
  console.log('\n🎉 问题修复脚本执行完成！');
}

// 执行修复流程
try {
  ensureDirectories();
  createBabelRuntimeFiles();
  createProjectConfig();
  clearCache();
  outputSuggestions();
} catch (error) {
  console.error('❌ 修复过程中出现错误:', error.message);
  process.exit(1);
}
