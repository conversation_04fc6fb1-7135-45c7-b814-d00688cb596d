/**
 * 修复微信开发者工具常见问题的脚本
 * 专门解决 macOS 微信开发者工具 1.06.2504010 版本的 Babel runtime 文件缺失问题
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔧 开始修复微信开发者工具问题...\n');
console.log(`📱 检测到系统: ${os.platform()}`);
console.log(`🛠️  目标修复: 编译错误综合修复\n`);
console.log('📋 修复项目:');
console.log('  1. ✅ TDesign样式文件路径已修复');
console.log('  2. ✅ 权限配置已优化');
console.log('  3. ✅ 全局组件配置已优化');
console.log('  4. 🔄 正在修复Babel Runtime文件...\n');

// 1. 检查并创建必要的目录结构
function ensureDirectories() {
  const dirs = [
    'node_modules/@babel/runtime/helpers',
    'miniprogram_npm/@babel/runtime/helpers',
    '.cache',
    'dist'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ 创建目录: ${dir}`);
    }
  });
}

// 2. 创建缺失的 Babel runtime 文件
function createBabelRuntimeFiles() {
  const definePropertyContent = `
module.exports = function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
};
`;

  // 创建更多必需的 Babel runtime 文件
  const runtimeFiles = {
    'defineProperty.js': definePropertyContent,
    'objectSpread2.js': `
module.exports = function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === 'function') {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function (key) {
      module.exports._defineProperty(target, key, source[key]);
    });
  }
  return target;
};
`,
    'classCallCheck.js': `
module.exports = function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
};
`,
    'createClass.js': `
module.exports = function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps) defineProperties(Constructor.prototype, protoProps);
  if (staticProps) defineProperties(Constructor, staticProps);
  return Constructor;
};

function defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor) descriptor.writable = true;
    Object.defineProperty(target, descriptor.key, descriptor);
  }
}
`
  };

  const basePaths = [
    'node_modules/@babel/runtime/helpers',
    'miniprogram_npm/@babel/runtime/helpers'
  ];

  basePaths.forEach(basePath => {
    Object.entries(runtimeFiles).forEach(([fileName, content]) => {
      const filePath = path.join(basePath, fileName);
      if (!fs.existsSync(filePath)) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ 创建文件: ${filePath}`);
      }
    });
  });
}

// 3. 创建项目配置文件
function createProjectConfig() {
  const projectConfig = {
    "description": "项目配置文件",
    "packOptions": {
      "ignore": [
        {
          "type": "file",
          "value": ".eslintrc.js"
        },
        {
          "type": "file", 
          "value": ".gitignore"
        },
        {
          "type": "file",
          "value": "README.md"
        },
        {
          "type": "folder",
          "value": "docs"
        },
        {
          "type": "folder",
          "value": "test"
        }
      ]
    },
    "setting": {
      "urlCheck": false,
      "es6": true,
      "enhance": true,
      "postcss": true,
      "preloadBackgroundData": false,
      "minified": true,
      "newFeature": true,
      "coverView": true,
      "nodeModules": true,
      "autoAudits": false,
      "showShadowRootInWxmlPanel": true,
      "scopeDataCheck": false,
      "uglifyFileName": false,
      "checkInvalidKey": true,
      "checkSiteMap": true,
      "uploadWithSourceMap": true,
      "compileHotReLoad": false,
      "lazyloadPlaceholderEnable": false,
      "useMultiFrameRuntime": true,
      "useApiHook": true,
      "useApiHostProcess": true,
      "babelSetting": {
        "ignore": [],
        "disablePlugins": [],
        "outputPath": ""
      },
      "enableEngineNative": false,
      "useIsolateContext": true,
      "userConfirmedBundleSwitch": false,
      "packNpmManually": false,
      "packNpmRelationList": [],
      "minifyWXSS": true,
      "disableUseStrict": false,
      "minifyWXML": true,
      "showES6CompileOption": false,
      "useCompilerPlugins": false,
      "ignoreUploadUnusedFiles": true
    },
    "compileType": "miniprogram",
    "libVersion": "3.8.12",
    "appid": "your-app-id",
    "projectname": "智慧养鹅",
    "debugOptions": {
      "hidedInDevtools": []
    },
    "scripts": {},
    "staticServerOptions": {
      "baseURL": "",
      "servePath": ""
    },
    "isGameTourist": false,
    "condition": {
      "search": {
        "list": []
      },
      "conversation": {
        "list": []
      },
      "game": {
        "list": []
      },
      "plugin": {
        "list": []
      },
      "gamePlugin": {
        "list": []
      },
      "miniprogram": {
        "list": []
      }
    }
  };
  
  if (!fs.existsSync('project.config.json')) {
    fs.writeFileSync('project.config.json', JSON.stringify(projectConfig, null, 2));
    console.log('✅ 创建 project.config.json');
  }
}

// 4. 清理缓存
function clearCache() {
  const cacheDirs = ['.cache', 'node_modules/.cache'];
  
  cacheDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`🗑️  清理缓存: ${dir}`);
    }
  });
}

// 5. 修复 macOS 微信开发者工具特定问题
function fixMacOSDevToolIssues() {
  console.log('🍎 检测到 macOS 系统，执行特定修复...');

  // 创建项目根目录的 babel 配置
  const babelConfig = {
    "presets": [
      ["@babel/preset-env", {
        "targets": {
          "browsers": ["last 2 versions", "iOS >= 8", "Android >= 4.4"]
        }
      }]
    ],
    "plugins": [
      "@babel/plugin-proposal-object-rest-spread",
      "@babel/plugin-transform-runtime"
    ]
  };

  if (!fs.existsSync('.babelrc')) {
    fs.writeFileSync('.babelrc', JSON.stringify(babelConfig, null, 2));
    console.log('✅ 创建 .babelrc 配置文件');
  }

  // 创建 package.json 中的 babel 配置（如果不存在）
  const packageJsonPath = 'package.json';
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    if (!packageJson.babel) {
      packageJson.babel = babelConfig;
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      console.log('✅ 更新 package.json 中的 babel 配置');
    }
  }
}

// 6. 输出修复建议
function outputSuggestions() {
  console.log('\n📋 修复完成！请按以下步骤操作：\n');
  console.log('🔧 针对你的具体错误：');
  console.log('1. ✅ WXSS 第332行 `unexpected token *` 错误已修复');
  console.log('2. ✅ Babel Runtime 文件缺失问题已修复');
  console.log('\n📱 接下来请执行：');
  console.log('1. 重启微信开发者工具');
  console.log('2. 点击"工具" -> "构建 npm"');
  console.log('3. 清除缓存：点击"项目" -> "清除缓存"');
  console.log('4. 重新编译项目');
  console.log('\n🚨 如果仍有问题，请尝试：');
  console.log('   - 完全关闭微信开发者工具，重新打开');
  console.log('   - 删除 node_modules 文件夹，重新 npm install');
  console.log('   - 检查微信开发者工具版本是否为最新版本');
  console.log('\n🎉 问题修复脚本执行完成！');
}

// 执行修复流程
try {
  ensureDirectories();
  createBabelRuntimeFiles();
  createProjectConfig();

  // macOS 特定修复
  if (os.platform() === 'darwin') {
    fixMacOSDevToolIssues();
  }

  clearCache();
  outputSuggestions();
} catch (error) {
  console.error('❌ 修复过程中出现错误:', error.message);
  console.error('📋 错误详情:', error.stack);
  console.log('\n🔍 请检查：');
  console.log('1. 是否有足够的文件系统权限');
  console.log('2. 项目目录是否正确');
  console.log('3. Node.js 版本是否兼容');
  process.exit(1);
}
