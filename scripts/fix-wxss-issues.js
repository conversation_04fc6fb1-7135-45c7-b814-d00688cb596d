/**
 * 修复 WXSS 编译错误的专用脚本
 * 专门解决微信小程序中不支持的 CSS 语法问题
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 开始修复 WXSS 编译错误...\n');

// 不支持的 CSS 选择器和属性映射
const UNSUPPORTED_PATTERNS = [
  {
    pattern: /\*\s*\{/g,
    replacement: 'view, text, image, button, input, textarea, picker, scroll-view, swiper, navigator, form {',
    description: '通用选择器 * 替换为具体组件选择器'
  },
  {
    pattern: /\*::before\s*,\s*\*::after/g,
    replacement: 'view::before, view::after, text::before, text::after',
    description: '通用伪元素选择器替换为具体组件选择器'
  },
  {
    pattern: /-webkit-overflow-scrolling:\s*touch;/g,
    replacement: '/* -webkit-overflow-scrolling: touch; 小程序不支持 */',
    description: '移除不支持的 webkit 属性'
  },
  {
    pattern: /-webkit-text-size-adjust:\s*100%;/g,
    replacement: '/* -webkit-text-size-adjust: 100%; 小程序不支持 */',
    description: '移除不支持的 webkit 文本调整属性'
  },
  {
    pattern: /will-change:\s*auto;/g,
    replacement: '/* will-change: auto; 小程序不支持 */',
    description: '移除不支持的 will-change 属性'
  }
];

// 检查并修复 WXSS 文件
function fixWxssFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return false;
  }

  console.log(`🔍 检查文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  let changeLog = [];

  // 应用修复规则
  UNSUPPORTED_PATTERNS.forEach(({ pattern, replacement, description }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      hasChanges = true;
      changeLog.push(`  ✅ ${description}`);
    }
  });

  // 如果有修改，写回文件
  if (hasChanges) {
    // 创建备份
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    console.log(`📦 创建备份: ${backupPath}`);
    
    // 写入修复后的内容
    fs.writeFileSync(filePath, content);
    console.log(`✅ 修复完成: ${filePath}`);
    changeLog.forEach(log => console.log(log));
    
    return true;
  } else {
    console.log(`✨ 文件无需修复: ${filePath}`);
    return false;
  }
}

// 递归查找并修复所有 WXSS 文件
function findAndFixWxssFiles(dir) {
  const files = fs.readdirSync(dir);
  let totalFixed = 0;

  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // 跳过 node_modules 和其他不需要的目录
      if (!['node_modules', '.git', 'dist', '.cache'].includes(file)) {
        totalFixed += findAndFixWxssFiles(fullPath);
      }
    } else if (file.endsWith('.wxss')) {
      if (fixWxssFile(fullPath)) {
        totalFixed++;
      }
    }
  });

  return totalFixed;
}

// 验证修复结果
function validateFixes() {
  console.log('\n🔍 验证修复结果...');
  
  const appWxssPath = 'app.wxss';
  if (fs.existsSync(appWxssPath)) {
    const content = fs.readFileSync(appWxssPath, 'utf8');
    const lines = content.split('\n');
    
    // 检查第332行附近是否还有问题
    const problemLines = lines.filter((line, index) => {
      return line.includes('*') && !line.includes('/*') && !line.includes('*/');
    });
    
    if (problemLines.length === 0) {
      console.log('✅ 验证通过：未发现通用选择器问题');
    } else {
      console.log('⚠️  验证发现问题：');
      problemLines.forEach((line, index) => {
        console.log(`   第${index + 1}行: ${line.trim()}`);
      });
    }
  }
}

// 主执行流程
try {
  console.log('📂 扫描项目中的 WXSS 文件...\n');
  
  const totalFixed = findAndFixWxssFiles('.');
  
  console.log(`\n📊 修复统计:`);
  console.log(`   修复文件数: ${totalFixed}`);
  
  validateFixes();
  
  console.log('\n🎉 WXSS 修复脚本执行完成！');
  console.log('\n📋 接下来请：');
  console.log('1. 重启微信开发者工具');
  console.log('2. 重新编译项目');
  console.log('3. 检查是否还有编译错误');
  
} catch (error) {
  console.error('❌ 修复过程中出现错误:', error.message);
  console.error('📋 错误详情:', error.stack);
  process.exit(1);
}
