/**
 * 全局样式 - 智慧养鹅小程序
 * 基于TDesign设计系统和微信小程序最佳实践
 * 版本: 3.0
 */

/* 引入TDesign小程序组件库基础样式 */
@import "tdesign-miniprogram/style/index.wxss";

/* 引入设计令牌 */
@import "styles/design-tokens.wxss";

/* 全局样式重置 - 基于现代CSS标准 */
page {
  height: 100%;
  font-size: var(--td-font-size-base, 28rpx);
  line-height: var(--td-line-height-base, 1.5);
  color: var(--td-text-color-primary, #000000);
  background-color: var(--td-bg-color-page, #f5f5f5);
  font-family: var(--td-font-family-base, -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, Roboto, 'Helvetica Neue', Arial, sans-serif);
  /* 性能优化 */
  will-change: auto;
  -webkit-overflow-scrolling: touch;
  /* 无障碍优化 */
  -webkit-text-size-adjust: 100%;
}

/* 全局盒模型设置 */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

/* 性能优化 - 减少重排重绘 */
view,
text,
image,
button,
input,
textarea,
picker,
scroll-view {
  box-sizing: border-box;
  position: relative;
}

/* 通用布局容器 */
.page-container {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
  padding-bottom: env(safe-area-inset-bottom);
}

.content-container {
  padding: var(--td-spacer, 32rpx);
  max-width: 750rpx;
  margin: 0 auto;
}

.safe-area-bottom {
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--td-spacer-2, 16rpx));
}

/* TDesign主题扩展 - 模块化主题色彩 */
.theme-health {
  --td-brand-color: #52c41a;
  --td-brand-color-light: #73d13d;
  --td-brand-color-dark: #389e0d;
  --module-bg-color: #f6ffed;
}

.theme-production {
  --td-brand-color: #1890ff;
  --td-brand-color-light: #40a9ff;
  --td-brand-color-dark: #096dd9;
  --module-bg-color: #f0f9ff;
}

.theme-shop {
  --td-brand-color: #fa8c16;
  --td-brand-color-light: #ffa940;
  --td-brand-color-dark: #d46b08;
  --module-bg-color: #fff7e6;
}

.theme-oa {
  --td-brand-color: #722ed1;
  --td-brand-color-light: #9254de;
  --td-brand-color-dark: #531dab;
  --module-bg-color: #f9f0ff;
}

/* 通用状态样式 - 基于TDesign规范 */
.status-success {
  color: var(--td-success-color, #52c41a);
}

.status-warning {
  color: var(--td-warning-color, #fa8c16);
}

.status-error {
  color: var(--td-error-color, #ff4d4f);
}

.status-info {
  color: var(--td-brand-color, #0066cc);
}

/* 通用卡片样式 - 遵循TDesign设计规范 */
.td-card {
  background: var(--td-bg-color-container, #ffffff);
  border-radius: var(--td-radius-default, 12rpx);
  box-shadow: var(--td-shadow-1, 0 2rpx 4rpx rgba(0, 0, 0, 0.05));
  border: 2rpx solid var(--td-border-level-1-color, transparent);
  overflow: hidden;
  margin-bottom: var(--td-spacer, 32rpx);
}

.td-card__header {
  padding: var(--td-spacer, 32rpx) var(--td-spacer, 32rpx) var(--td-spacer-2, 16rpx);
  border-bottom: 2rpx solid var(--td-border-level-1-color, #e7e7e7);
  background: var(--td-bg-color-container, #ffffff);
}

.td-card__title {
  font-size: var(--td-font-size-title-medium, 32rpx);
  font-weight: var(--td-font-weight-medium, 500);
  color: var(--td-text-color-primary, #000000);
  line-height: var(--td-line-height-title-medium, 1.4);
}

.td-card__subtitle {
  font-size: var(--td-font-size-body-small, 24rpx);
  color: var(--td-text-color-secondary, #666666);
  margin-top: var(--td-spacer-1, 8rpx);
}

.td-card__body {
  padding: var(--td-spacer, 32rpx);
}

.td-card__footer {
  padding: var(--td-spacer-2, 16rpx) var(--td-spacer, 32rpx) var(--td-spacer, 32rpx);
  border-top: 2rpx solid var(--td-border-level-1-color, #e7e7e7);
  background: var(--td-bg-color-secondarycontainer, #fafafa);
}

/* 列表样式 - 基于TDesign Cell组件规范 */
.td-list {
  background: var(--td-bg-color-container, #ffffff);
  border-radius: var(--td-radius-default, 12rpx);
  overflow: hidden;
}

.td-list-item {
  position: relative;
  padding: var(--td-spacer, 32rpx);
  border-bottom: 2rpx solid var(--td-border-level-1-color, #e7e7e7);
  transition: background-color var(--td-duration-base, 0.2s) ease;
}

.td-list-item:last-child {
  border-bottom: none;
}

.td-list-item--hover {
  background-color: var(--td-bg-color-component-hover, #f5f5f5);
}

.td-list-item--active {
  background-color: var(--td-bg-color-component-active, #eeeeee);
}

/* 分割线 - 基于TDesign Divider组件 */
.td-divider {
  height: 2rpx;
  background: var(--td-border-level-1-color, #e7e7e7);
  margin: var(--td-spacer, 32rpx) 0;
}

.td-divider--vertical {
  width: 2rpx;
  height: auto;
  margin: 0 var(--td-spacer, 32rpx);
  display: inline-block;
  vertical-align: middle;
}

.td-divider--dashed {
  border-top: 2rpx dashed var(--td-border-level-1-color, #e7e7e7);
  background: none;
  height: 0;
}

/* 加载状态 - 基于TDesign Loading组件 */
.td-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--td-spacer-3, 48rpx);
  color: var(--td-text-color-placeholder, #bbbbbb);
  font-size: var(--td-font-size-body-medium, 28rpx);
}

/* 空状态 - 基于TDesign Empty组件 */
.td-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--td-spacer-4, 64rpx) var(--td-spacer, 32rpx);
  text-align: center;
}

.td-empty__icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--td-spacer, 32rpx);
  opacity: 0.6;
}

.td-empty__text {
  font-size: var(--td-font-size-body-medium, 28rpx);
  color: var(--td-text-color-placeholder, #bbbbbb);
  line-height: var(--td-line-height-body-medium, 1.5);
}

.td-empty__description {
  font-size: var(--td-font-size-body-small, 24rpx);
  color: var(--td-text-color-disabled, #cccccc);
  margin-top: var(--td-spacer-1, 8rpx);
}

/* 错误状态 */
.td-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--td-spacer-4, 64rpx) var(--td-spacer, 32rpx);
  color: var(--td-error-color, #ff4d4f);
  text-align: center;
}

/* 通用工具类 - 基于TDesign规范 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.margin-top { margin-top: var(--td-spacer, 32rpx); }
.margin-bottom { margin-bottom: var(--td-spacer, 32rpx); }
.margin-left { margin-left: var(--td-spacer, 32rpx); }
.margin-right { margin-right: var(--td-spacer, 32rpx); }

.padding { padding: var(--td-spacer, 32rpx); }
.padding-small { padding: var(--td-spacer-2, 16rpx); }
.padding-large { padding: var(--td-spacer-3, 48rpx); }

/* 响应式设计 - 适配不同屏幕尺寸 */
@media (max-width: 350px) {
  page {
    font-size: var(--td-font-size-body-small, 24rpx);
  }
  
  .content-container {
    padding: var(--td-spacer-2, 16rpx);
  }
}

@media (min-width: 768px) {
  .content-container {
    max-width: 600rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  page {
    background-color: var(--td-bg-color-page-dark, #1a1a1a);
    color: var(--td-text-color-primary-dark, #ffffff);
  }
  
  .td-card {
    background: var(--td-bg-color-container-dark, #2a2a2a);
    border-color: var(--td-border-level-1-color-dark, #3a3a3a);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .td-card {
    border-width: 4rpx;
    border-color: var(--td-text-color-primary, #000000);
  }
  
  .td-divider {
    height: 4rpx;
    background: var(--td-text-color-primary, #000000);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 屏幕阅读器优化 */
.sr-only {
  position: absolute !important;
  width: 2rpx !important;
  height: 2rpx !important;
  padding: 0 !important;
  margin: -2rpx !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 焦点可见性优化 */
.focus-visible {
  outline: 4rpx solid var(--td-brand-color, #0066cc);
  outline-offset: 4rpx;
}

/* 禁用状态 */
.disabled {
  opacity: var(--td-opacity-disabled, 0.4);
  pointer-events: none;
  cursor: not-allowed;
}