import{QrCode,QrSegment}from"../../common/shared/qrcode/qrcodegen";import{ERROR_LEVEL_MAP,getImageSettings,getMarginSize}from"../../common/shared/qrcode/utils";const useQRCode=e=>{const{value:t,level:n,minVersion:r,includeMargin:g,marginSize:o,imageSettings:m,size:i}=e,s=(()=>{const e=QrSegment.makeSegments(t);return QrCode.encodeSegments(e,ERROR_LEVEL_MAP[n],r)})(),a=s.getModules(),d=getMarginSize(g,o),c=getImageSettings(a,i,d,m);return{cells:a,margin:d,numCells:a.length+2*d,calculatedImageSettings:c,qrcode:s}};export default useQRCode;