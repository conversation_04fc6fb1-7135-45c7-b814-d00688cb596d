<wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}} width:{{size}}px; height: {{size}}px; background-color: {{bgColor}};" class="{{classPrefix}} {{borderless ? prefix+'-'+'borderless' : ''}} {{prefix}}-class"><qrcode-canvas id="qrcodeCanvas" size="{{size}}" value="{{value}}" level="{{level}}" color="{{color}}" bg-color="{{bgColor}}" icon="{{icon}}" icon-size="{{iconSize}}" bind:drawError="handleDrawError" bind:drawCompleted="handleDrawCompleted"></qrcode-canvas><block wx:if="{{showMask && canvasReady}}"><view class="{{prefix}}-mask"><qrcode-status status="{{status}}" bind:refresh="handleRefresh" status-render="{{statusRender}}"><slot name="statusRender" slot="statusRender"></slot></qrcode-status></view></block></view>