import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import useCustomNavbar from"../mixins/using-custom-navbar";import{unitConvert,systemInfo}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-fab`,baseButtonProps={size:"large",shape:"circle",theme:"primary",tClass:`${prefix}-fab__button`};let Fab=class extends SuperComponent{constructor(){super(...arguments),this.behaviors=[useCustomNavbar],this.properties=props,this.externalClasses=["class",`${prefix}-class`,`${prefix}-class-button`],this.data={prefix:prefix,classPrefix:name,buttonData:baseButtonProps,moveStyle:null},this.observers={"buttonProps.**, icon, text, ariaLabel, yBounds"(){var t;this.setData({buttonData:Object.assign(Object.assign(Object.assign(Object.assign({},baseButtonProps),{shape:this.properties.text?"round":"circle"}),this.properties.buttonProps),{icon:this.properties.icon,content:this.properties.text,ariaLabel:this.properties.ariaLabel})},null===(t=this.computedSize)||void 0===t?void 0:t.bind(this))}},this.methods={onTplButtonTap(t){this.triggerEvent("click",t)},onStart(t){this.triggerEvent("dragstart",t.detail.e)},onMove(t){const{yBounds:e}=this.properties,{distanceTop:o}=this.data,{x:s,y:i,rect:r}=t.detail,a=systemInfo.windowWidth-r.width,n=systemInfo.windowHeight-Math.max(o,unitConvert(e[0]))-r.height,p=Math.max(0,Math.min(s,a)),m=Math.max(0,unitConvert(e[1]),Math.min(i,n));this.setData({moveStyle:`right: ${p}px; bottom: ${m}px;`})},onEnd(t){this.triggerEvent("dragend",t.detail.e)},computedSize(){var t,e;if(!this.properties.draggable)return;const o=this.selectComponent("#draggable");(null===(e=null===(t=this.properties)||void 0===t?void 0:t.yBounds)||void 0===e?void 0:e[1])?this.setData({moveStyle:`bottom: ${unitConvert(this.properties.yBounds[1])}px`},o.computedRect):o.computedRect()}}}};Fab=__decorate([wxComponent()],Fab);export default Fab;