<import src="../../common/template/button.wxml"/><wxs src="../../common/utils.wxs" module="_"/><template name="draggable"><t-draggable id="draggable" style="right: 16px; bottom: 32px; {{_._style([style, customStyle, moveStyle])}}" direction="{{draggable === true ? 'all' : draggable}}" bind:start="onStart" bind:move="onMove" bind:end="onEnd"><slot wx:if="{{!buttonData.content && !buttonData.icon}}"/><template wx:else is="button" data="{{useDefaultSlot: true, ...buttonData}}"/></t-draggable></template>