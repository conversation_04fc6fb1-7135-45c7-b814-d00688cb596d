import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import props from"./props";import config from"../common/config";import{setIcon}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-empty`;let default_1=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`,`${prefix}-class-description`,`${prefix}-class-image`],this.properties=props,this.data={prefix:prefix,classPrefix:name},this.observers={icon(e){const o=setIcon("icon",e,"");this.setData(Object.assign({},o))}}}};default_1=__decorate([wxComponent()],default_1);export default default_1;