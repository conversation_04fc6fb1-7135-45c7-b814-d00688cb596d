<wxs src="../common/utils.wxs" module="_"/><view wx:if="{{realVisible && preventScrollThrough}}" class="{{prefix}}-overlay {{transitionClass}} class" style="{{_._style(['--td-overlay-transition-duration:' + duration + 'ms', 'z-index:' + _zIndex, 'top:' + distanceTop + 'px', computedStyle, style, customStyle])}}" bind:tap="handleClick" catchtouchmove="noop" bind:transitionend="onTransitionEnd" aria-role="{{ ariaRole || 'button' }}" aria-label="{{ ariaLabel || '关闭' }}"><slot/></view><view wx:elif="{{realVisible}}" class="{{prefix}}-overlay {{transitionClass}} class" style="{{_._style(['z-index:' + _zIndex, 'top:' + distanceTop + 'px', computedStyle, style, customStyle])}}" bind:tap="handleClick" bind:transitionend="onTransitionEnd" aria-role="{{ ariaRole || 'button' }}" aria-label="{{ ariaLabel || '关闭' }}"><slot/></view>