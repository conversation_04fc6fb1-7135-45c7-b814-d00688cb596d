import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import transition from"../mixins/transition";import useCustomNavbar from"../mixins/using-custom-navbar";const{prefix:prefix}=config,name=`${prefix}-overlay`;let Overlay=class extends SuperComponent{constructor(){super(...arguments),this.properties=props,this.behaviors=[transition(),useCustomNavbar],this.data={prefix:prefix,classPrefix:name,computedStyle:"",_zIndex:11e3},this.observers={backgroundColor(o){this.setData({computedStyle:o?`background-color: ${o};`:""})},zIndex(o){0!==o&&this.setData({_zIndex:o})}},this.methods={handleClick(){this.triggerEvent("click",{visible:!this.properties.visible})},noop(){}}}};Overlay=__decorate([wxComponent()],Overlay);export default Overlay;