import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getRect,throttle,systemInfo}from"../common/utils";import pageScrollMixin from"../mixins/page-scroll";const{prefix:prefix}=config,name=`${prefix}-indexes`;let Indexes=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-sidebar`,`${prefix}-class-sidebar-item`],this.properties=props,this.controlledProps=[{key:"current",event:"change"}],this.data={prefix:prefix,classPrefix:name,_height:0,_indexList:[],scrollTop:0,activeAnchor:null,showTips:!1},this.relations={"../indexes-anchor/indexes-anchor":{type:"child"}},this.behaviors=[pageScrollMixin()],this.timer=null,this.groupTop=[],this.sidebar=null,this.observers={indexList(t){this.setIndexList(t),this.setHeight(this.data._height)},height(t){this.setHeight(t)},current(t){t&&this.data.activeAnchor&&t!==this.data.activeAnchor&&this.setAnchorByCurrent(t,"update")}},this.lifetimes={ready(){this.timer=null,this.groupTop=[],this.sidebar=null,0===this.data._height&&this.setHeight(),null===this.data.indexList&&this.setIndexList()}},this.methods={setHeight(t){if(!t){const{windowHeight:e}=systemInfo;t=e}this.setData({_height:t},()=>{this.getAllRect()})},setIndexList(t){if(t)this.setData({_indexList:t});else{const t="A".charCodeAt(0),e=[];for(let i=t,s=t+26;i<s;i+=1)e.push(String.fromCharCode(i));this.setData({_indexList:e})}},getAllRect(){this.getAnchorsRect().then(()=>{this.groupTop.forEach((t,e)=>{const i=this.groupTop[e+1];t.totalHeight=((null==i?void 0:i.top)||1/0)-t.top});const t=this.data.current||this.data._indexList[0];this.setAnchorByCurrent(t,"init")}),this.getSidebarRect()},getAnchorsRect(){return Promise.all(this.$children.map(t=>getRect(t,`.${name}-anchor`).then(e=>{this.groupTop.push({height:e.height,top:e.top,anchor:t.data.index})})))},getSidebarRect(){getRect(this,`#id-${name}__bar`).then(t=>{const{top:e,height:i}=t,{length:s}=this.data._indexList;this.sidebar={top:e,height:i,itemHeight:(i-2*(s-1))/s}})},toggleTips(t){t?this.setData({showTips:!0}):(clearInterval(this.timer),this.timer=setTimeout(()=>{this.setData({showTips:!1})},300))},setAnchorByCurrent(t,e){const{stickyOffset:i}=this.data;if(null!==this.data.activeAnchor&&this.data.activeAnchor===t)return;const s=this.groupTop.find(e=>e.anchor===t);if(s){const o=s.top-i;0===o&&"init"===e?this.setAnchorOnScroll(o):wx.pageScrollTo({scrollTop:o,duration:0}),["click","touch"].includes(e)&&(this.toggleTips(!0),this.triggerEvent("select",{index:t}))}},onClick(t){const{current:e}=t.currentTarget.dataset;this.setAnchorByCurrent(e,"click")},onTouchMove(t){this.onAnchorTouch(t)},onTouchCancel(){this.toggleTips(!1)},onTouchEnd(t){this.toggleTips(!1),this.onAnchorTouch(t)},onAnchorTouch:throttle(function(t){const e=(t=>{const e=t-this.sidebar.top;return e<=0?0:e>this.sidebar.height?this.data._indexList.length-1:Math.floor(e/this.sidebar.itemHeight)})(t.changedTouches[0].clientY);this.setAnchorByCurrent(this.data._indexList[e],"touch")},1e3/30),setAnchorOnScroll(t){if(!this.groupTop)return;const{sticky:e,stickyOffset:i}=this.data;t+=i;const s=this.groupTop.findIndex(e=>t>=e.top-e.height&&t<=e.top+e.totalHeight-e.height);if(-1===s)return;const o=this.groupTop[s];if(this.setData({activeAnchor:o.anchor},()=>{this._trigger("change",{index:o.anchor,current:o.anchor})}),e){const e=o.top-t,h=e<o.height&&e>0&&t>i;this.$children.forEach((r,n)=>{if(n===s){const s=t>i,n=`transform: translate3d(0, ${h?e:0}px, 0); top: ${i}px`;n===r.data.anchorStyle&&s===r.data.sticky||r.setData({sticky:s,active:!0,style:`height: ${o.height}px`,anchorStyle:n})}else if(n+1===s){const t=`transform: translate3d(0, ${h?e-o.height:0}px, 0); top: ${i}px`;t!==r.data.anchorStyle&&r.setData({sticky:!0,active:!0,style:`height: ${o.height}px`,anchorStyle:t})}else r.setData({active:!1,sticky:!1,anchorStyle:""})})}},onScroll({scrollTop:t}){this.setAnchorOnScroll(t)}}}};Indexes=__decorate([wxComponent()],Indexes);export default Indexes;