import{__rest}from"tslib";import{getInstance}from"../common/utils";function Toast(t){var o;const{context:s,selector:e="#t-toast"}=t,n=__rest(t,["context","selector"]),a=getInstance(s,e);a&&a.show(Object.assign(Object.assign({},n),{duration:null!==(o=n.duration)&&void 0!==o?o:2e3}))}function showToast(t={}){Toast(t)}function hideToast(t={}){const{context:o,selector:s="#t-toast"}=t,e=getInstance(o,s);e&&e.hide()}export{Toast as default,showToast,hideToast};