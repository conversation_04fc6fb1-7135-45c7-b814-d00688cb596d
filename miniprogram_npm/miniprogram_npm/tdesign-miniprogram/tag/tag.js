import{__decorate}from"tslib";import{wxComponent,SuperComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{classNames,calcIcon}from"../common/utils";import{isNumber}from"../common/validator";const{prefix:prefix}=config,name=`${prefix}-tag`;let Tag=class extends SuperComponent{constructor(){super(...arguments),this.data={prefix:prefix,classPrefix:name,className:"",tagStyle:""},this.properties=props,this.externalClasses=[`${prefix}-class`],this.options={multipleSlots:!0},this.lifetimes={attached(){this.setClass(),this.setTagStyle()}},this.observers={"size, shape, theme, variant, closable, disabled"(){this.setClass()},maxWidth(){this.setTagStyle()},icon(s){this.setData({_icon:calcIcon(s)})},closable(s){this.setData({_closable:calcIcon(s,"close")})}},this.methods={setClass(){const{prefix:s,classPrefix:t}=this.data,{size:e,shape:a,theme:i,variant:o,closable:l,disabled:r}=this.properties,c=classNames([t,`${t}--${i||"default"}`,`${t}--${o}`,l?`${t}--closable ${s}-is-closable`:"",r?`${t}--disabled ${s}-is-disabled`:"",`${t}--${e}`,`${t}--${a}`]);this.setData({className:c})},setTagStyle(){const{maxWidth:s}=this.properties;if(!s)return"";const t=isNumber(s)?`${s}px`:s;this.setData({tagStyle:`max-width:${t};`})},handleClick(s){this.data.disabled||this.triggerEvent("click",s)},handleClose(s){this.data.disabled||this.triggerEvent("close",s)}}}};Tag=__decorate([wxComponent()],Tag);export default Tag;