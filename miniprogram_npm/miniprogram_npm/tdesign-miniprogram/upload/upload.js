import{__decorate,__rest}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import props from"./props";import config from"../common/config";import{isOverSize}from"../common/utils";import{isObject}from"../common/validator";const{prefix:prefix}=config,name=`${prefix}-upload`;let Upload=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.options={multipleSlots:!0},this.data={classPrefix:name,prefix:prefix,current:!1,proofs:[],customFiles:[],customLimit:0,column:4,dragBaseData:{},rows:0,dragWrapStyle:"",dragList:[],dragging:!0,dragLayout:!1},this.properties=props,this.controlledProps=[{key:"files",event:"success"}],this.observers={"files, max, draggable"(t,e){this.handleLimit(t,e)},gridConfig(){this.updateGrid()}},this.lifetimes={ready(){this.handleLimit(this.data.customFiles,this.data.max),this.updateGrid()}},this.methods={getPreviewMediaSources(){const t=[];return this.data.customFiles.forEach(e=>{const i={url:e.url,type:e.type,poster:e.thumb||void 0};t.push(i)}),t},onPreview(t){this.onFileClick(t);const{preview:e}=this.properties;if(!e)return;this.data.customFiles.some(t=>"video"===t.type)?this.onPreviewMedia(t):this.onPreviewImage(t)},onPreviewImage(t){var e;const{index:i}=t.currentTarget.dataset,s=this.data.customFiles.filter(t=>-1!==t.percent).map(t=>t.url),r=null===(e=this.data.customFiles[i])||void 0===e?void 0:e.url;wx.previewImage({urls:s,current:r,fail(){wx.showToast({title:"预览图片失败",icon:"none"})}})},onPreviewMedia(t){const{index:e}=t.currentTarget.dataset,i=this.getPreviewMediaSources();wx.previewMedia({sources:i,current:e,fail(){wx.showToast({title:"预览视频失败",icon:"none"})}})},uploadFiles(t){return new Promise(e=>{const i=this.data.requestMethod(t);if(i instanceof Promise)return i;e({})})},startUpload(t){return"function"==typeof this.data.requestMethod?this.uploadFiles(t).then(()=>{t.forEach(t=>{t.percent=100}),this.triggerSuccessEvent(t)}).catch(t=>{this.triggerFailEvent(t)}):(this.triggerSuccessEvent(t),this.handleLimit(this.data.customFiles,this.data.max),Promise.resolve())},onAddTap(){const{disabled:t,mediaType:e,source:i}=this.properties;t||("media"===i?this.chooseMedia(e):this.chooseMessageFile(e))},chooseMedia(t){const{customLimit:e}=this.data,{config:i,sizeLimit:s}=this.properties;wx.chooseMedia(Object.assign(Object.assign({count:Math.min(20,e),mediaType:t},i),{success:e=>{const i=[];e.tempFiles.forEach(e=>{const{size:r,fileType:a,tempFilePath:o,width:n,height:l,duration:c,thumbTempFilePath:h}=e,d=__rest(e,["size","fileType","tempFilePath","width","height","duration","thumbTempFilePath"]);if(isOverSize(r,s)){let t=("image"===a?"图片":"视频")+"大小超过限制";return"number"!=typeof s&&(t=s.message.replace("{sizeLimit}",null==s?void 0:s.size)),void wx.showToast({icon:"none",title:t})}const g=this.getRandFileName(o);i.push(Object.assign({name:g,type:this.getFileType(t,o,a),url:o,size:r,width:n,height:l,duration:c,thumb:h,percent:0},d))}),this.afterSelect(i)},fail:t=>{this.triggerFailEvent(t)},complete:t=>{this.triggerEvent("complete",t)}}))},chooseMessageFile(t){const{customLimit:e}=this.data,{config:i,sizeLimit:s}=this.properties;wx.chooseMessageFile(Object.assign(Object.assign({count:Math.min(100,e),type:Array.isArray(t)?"all":t},i),{success:e=>{const i=[];e.tempFiles.forEach(e=>{const{size:r,type:a,path:o}=e,n=__rest(e,["size","type","path"]);if(isOverSize(r,s)){let t=("image"===a?"图片":"视频")+"大小超过限制";return"number"!=typeof s&&(t=s.message.replace("{sizeLimit}",null==s?void 0:s.size)),void wx.showToast({icon:"none",title:t})}const l=this.getRandFileName(o);i.push(Object.assign({name:l,type:this.getFileType(t,o,a),url:o,size:r,percent:0},n))}),this.afterSelect(i)},fail:t=>this.triggerFailEvent(t),complete:t=>this.triggerEvent("complete",t)}))},afterSelect(t){this._trigger("select-change",{files:[...this.data.customFiles],currentSelectedFiles:[t]}),this._trigger("add",{files:t}),this.startUpload(t)},dragVibrate(t){var e;const{vibrateType:i}=t,{draggable:s}=this.data,r=null===(e=null==s?void 0:s.vibrate)||void 0===e||e,a=null==s?void 0:s.collisionVibrate;(r&&"longPress"===i||a&&"touchMove"===i)&&wx.vibrateShort({type:"light"})},dragStatusChange(t){const{dragging:e}=t;this.setData({dragging:e})},dragEnd(t){const{dragCollisionList:e}=t;let i=[];i=0===e.length?this.data.customFiles:e.reduce((t,e)=>{const{realKey:i,data:s,fixed:r}=e;return r||(t[i]=Object.assign({},s)),t},[]),this.triggerDropEvent(i)},triggerDropEvent(t){const{transition:e}=this.properties;if(e.backTransition){const i=setTimeout(()=>{this.triggerEvent("drop",{files:t}),clearTimeout(i)},e.duration)}else this.triggerEvent("drop",{files:t})}}}handleLimit(t,e){0===e&&(e=Number.MAX_SAFE_INTEGER),this.setData({customFiles:t.length>e?t.slice(0,e):t,customLimit:e-t.length,dragging:!0}),this.initDragLayout()}triggerSuccessEvent(t){this._trigger("success",{files:[...this.data.customFiles,...t]})}triggerFailEvent(t){this.triggerEvent("fail",t)}onFileClick(t){const{file:e}=t.currentTarget.dataset;this.triggerEvent("click",{file:e})}getFileType(t,e,i){if(i)return i;if(1===t.length)return t[0];const s=e.split("."),r=s[s.length-1];return["avi","wmv","mkv","mp4","mov","rm","3gp","flv","mpg","rmvb"].includes(r.toLocaleLowerCase())?"video":"image"}getRandFileName(t){const e=t.lastIndexOf("."),i=-1===e?"":t.substr(e);return parseInt(`${Date.now()}${Math.floor(900*Math.random()+100)}`,10).toString(36)+i}onDelete(t){const{index:e}=t.currentTarget.dataset;this.deleteHandle(e)}deleteHandle(t){const{customFiles:e}=this.data,i=e[t];this.triggerEvent("remove",{index:t,file:i})}updateGrid(){let{gridConfig:t={}}=this.properties;isObject(t)||(t={});const{column:e=4,width:i=160,height:s=160}=t;this.setData({gridItemStyle:`width:${i}rpx;height:${s}rpx`,column:e})}initDragLayout(){const{draggable:t,disabled:e}=this.properties;t&&!e&&(this.initDragList(),this.initDragBaseData())}initDragList(){let t=0;const{column:e,customFiles:i,customLimit:s}=this.data,r=[];if(i.forEach((i,s)=>{r.push({realKey:t,sortKey:s,tranX:s%e*100+"%",tranY:100*Math.floor(s/e)+"%",data:Object.assign({},i)}),t+=1}),s>0){const t=r.length;r.push({realKey:t,sortKey:t,tranX:t%e*100+"%",tranY:100*Math.floor(t/e)+"%",fixed:!0})}this.data.rows=Math.ceil(r.length/e),this.setData({dragList:r})}initDragBaseData(){const{classPrefix:t,rows:e,column:i,customFiles:s}=this.data;if(0===s.length)return void this.setData({dragBaseData:{},dragWrapStyle:"",dragLayout:!1});const r=this.createSelectorQuery(),a=`.${t} >>> .t-grid-item`,o=`.${t} >>> .t-grid`;r.select(a).boundingClientRect(),r.select(o).boundingClientRect(),r.selectViewport().scrollOffset(),r.exec(s=>{const[{width:r,height:a},{left:o,top:n},{scrollTop:l}]=s,c={rows:e,classPrefix:t,itemWidth:r,itemHeight:a,wrapLeft:o,wrapTop:n+l,columns:i},h=`height: ${e*a}px`;this.setData({dragBaseData:c,dragWrapStyle:h,dragLayout:!0},()=>{const t=setTimeout(()=>{this.setData({dragging:!1}),clearTimeout(t)},0)})})}};Upload=__decorate([wxComponent()],Upload);export default Upload;