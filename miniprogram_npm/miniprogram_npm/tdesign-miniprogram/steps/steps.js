import{__decorate}from"tslib";import{wxComponent,SuperComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-steps`;let Steps=class extends SuperComponent{constructor(){super(...arguments),this.relations={"../step-item/step-item":{type:"child",linked(e){this.updateChildren();const{readonly:t}=this.data;e.setData({readonly:t})},unlinked(){this.updateLastChid()}}},this.externalClasses=[`${prefix}-class`],this.properties=props,this.controlledProps=[{key:"current",event:"change"}],this.data={prefix:prefix,classPrefix:name},this.observers={"current, theme, sequence"(){this.updateChildren()}},this.methods={updateChildren(){const e=this.$children;e.forEach((t,s)=>{t.updateStatus(Object.assign({index:s,items:e},this.data))})},updateLastChid(){const e=this.$children;e.forEach((t,s)=>t.setData({isLastChild:s===e.length-1}))},handleClick(e){if(!this.data.readonly){const t=this.data.current;this._trigger("change",{previous:t,current:e})}}}}};Steps=__decorate([wxComponent()],Steps);export default Steps;