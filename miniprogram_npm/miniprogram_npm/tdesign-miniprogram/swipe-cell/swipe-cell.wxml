<import src="../common/template/icon.wxml"/><wxs src="./swipe-cell.wxs" module="swipe"></wxs><wxs src="../common/utils.wxs" module="_"/><view class="class {{prefix}}-class {{classPrefix}}" style="{{_._style([style, customStyle])}}" data-key="cell" bind:tap="onTap" bind:touchstart="{{disabled || swipe.startDrag}}" bind:touchmove="{{skipMove ? '' : disabled || swipe.onDrag}}" bind:touchend="{{skipMove ? '' : disabled || swipe.endDrag}}" bind:touchcancel="{{disabled || swipe.endDrag}}" opened="{{opened}}" change:opened="{{swipe.onOpenedChange}}" leftWidth="{{leftWidth}}" rightWidth="{{rightWidth}}" change:leftWidth="{{swipe.initLeftWidth}}" change:rightWidth="{{swipe.initRightWidth}}"><view id="wrapper"><view class="{{classPrefix}}__left" data-key="left"><slot name="left"/><view wx:for="{{left}}" class="{{classPrefix}}__content {{item.className}}" style="{{item.style}}" wx:key="index" data-action="{{item}}" bind:tap="onActionTap"><template wx:if="{{item.icon}}" is="icon" data="{{tClass: classPrefix + '__icon', name: item.icon, ...item.icon}}"></template><text wx:if="{{item.text}}" class="{{classPrefix}}__text">{{item.text}}</text></view></view><slot/><view class="{{classPrefix}}__right" data-key="right"><slot name="right"/><view wx:for="{{right}}" class="{{classPrefix}}__content {{item.className}}" style="{{item.style}}" wx:key="index" data-action="{{item}}" bind:tap="onActionTap"><template wx:if="{{item.icon}}" is="icon" data="{{tClass: classPrefix + '__icon', name: item.icon, ...item.icon}}"></template><text wx:if="{{item.text}}" class="{{classPrefix}}__text">{{item.text}}</text></view></view></view></view>