<wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}}" class="{{classPrefix}} class {{prefix}}-class" aria-role="radiogroup"><slot/><block wx:for="{{radioOptions}}" wx:key="value"><t-radio class="{{prefix}}-radio-option" data-index="{{index}}" data-value="{{item.value}}" data-allow-uncheck="{{item.allowUncheck || allowUncheck}}" block="{{item.block || true}}" label="{{item.label || ''}}" value="{{item.value}}" checked="{{item.checked || false}}" content="{{item.content || ''}}" allow-uncheck="{{item.allowUncheck || allowUncheck}}" content-disabled="{{item.contentDisabled || false}}" readonly="{{item.readonly || false}}" disabled="{{item.disabled || false}}" icon="{{item.icon || icon}}" placement="{{item.placement || placement}}" max-content-row="{{item.maxContentRow || 5}}" max-label-row="{{item.maxLabelRow || 3}}" name="{{item.name || ''}}" borderless="{{borderless}}" bind:change="handleRadioChange"/></block></view>