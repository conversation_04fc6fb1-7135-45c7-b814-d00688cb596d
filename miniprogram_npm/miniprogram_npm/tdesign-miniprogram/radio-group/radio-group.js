import{__decorate}from"tslib";import config from"../common/config";import{SuperComponent,wxComponent}from"../common/src/index";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-radio-group`;let RadioGroup=class extends SuperComponent{constructor(){super(...arguments),this.behaviors=["wx://form-field"],this.externalClasses=[`${prefix}-class`],this.data={prefix:prefix,classPrefix:name,radioOptions:[]},this.relations={"../radio/radio":{type:"descendant",linked(e){const{value:t,disabled:i,readonly:o}=this.data;e.setData({checked:t===e.data.value}),e.setDisabled(i),e.setReadonly(o)}}},this.properties=props,this.controlledProps=[{key:"value",event:"change"}],this.observers={value(e){this.getChildren().forEach(t=>{t.setData({checked:e===t.data.value})})},options(){this.initWithOptions()},disabled(e){var t;(null===(t=this.data.options)||void 0===t?void 0:t.length)?this.initWithOptions():this.getChildren().forEach(t=>{t.setDisabled(e)})}},this.methods={getChildren(){let e=this.$children;return(null==e?void 0:e.length)||(e=this.selectAllComponents(`.${prefix}-radio-option`)),e},updateValue(e){this._trigger("change",{value:e})},handleRadioChange(e){const{checked:t}=e.detail,{value:i,index:o,allowUncheck:a}=e.target.dataset;this._trigger("change",!1===t&&a?{value:null,index:o}:{value:i,index:o})},initWithOptions(){const{options:e,value:t,keys:i,disabled:o,readonly:a}=this.data;if(!(null==e?void 0:e.length)||!Array.isArray(e))return void this.setData({radioOptions:[]});const s=[];try{e.forEach(e=>{var l,r,n;const d=typeof e;"number"===d||"string"===d?s.push({label:`${e}`,value:e,checked:t===e,disabled:o,readonly:a}):"object"===d&&s.push(Object.assign(Object.assign({},e),{label:e[null!==(l=null==i?void 0:i.label)&&void 0!==l?l:"label"],value:e[null!==(r=null==i?void 0:i.value)&&void 0!==r?r:"value"],checked:t===e[null!==(n=null==i?void 0:i.value)&&void 0!==n?n:"value"],disabled:e.disabled||o,readonly:e.readonly||a}))}),this.setData({radioOptions:s})}catch(e){console.error("error",e)}}}}};RadioGroup=__decorate([wxComponent()],RadioGroup);export default RadioGroup;