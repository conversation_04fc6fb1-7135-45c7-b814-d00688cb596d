<wxs src="../common/utils.wxs" module="_"/><view class="{{_.cls(classPrefix, [['fixed', fixed]])}} {{visibleClass}} class {{prefix}}-class" style="{{_._style([boxStyle, style, customStyle])}}"><view wx:if="{{fixed}}" class="{{classPrefix}}__placeholder {{prefix}}-class-placeholder"/><view class="{{classPrefix}}__content {{prefix}}-class-content"><view class="{{classPrefix}}__left {{hideLeft ? classPrefix + '__left--hide' : ''}} {{prefix}}-class-left"><view wx:if="{{leftArrow}}" class="{{classPrefix}}__btn" bind:tap="goBack" aria-role="button" aria-label="返回"><t-icon name="chevron-left" class="{{classPrefix}}__left-arrow"/></view><slot name="left"/><view class="{{classPrefix}}__capsule {{prefix}}-class-capsule"><slot name="capsule"/></view></view><view class="{{classPrefix}}__center {{hideCenter ? classPrefix + '__center--hide' : ''}} {{prefix}}-class-center"><slot name="title"/><text wx:if="{{title}}" class="{{classPrefix}}__center-title {{prefix}}-class-title">{{showTitle}}</text></view></view></view>