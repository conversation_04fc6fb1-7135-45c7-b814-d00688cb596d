<wxs module="_" src="../common/utils.wxs"/><wxs module="_this" src="./index.wxs"/><view class="{{classPrefix}} class" style="{{_._style(['height:' + _.addUnit(height) , style, customStyle])}}"><t-scroll-view class="{{_.cls(classPrefix + '__column', [_this.getTreeClass(leafLevel - level, treeOptions.length)])}} {{prefix}}-class" wx:for="{{treeOptions}}" wx:key="level" wx:for-index="level" scrollIntoView="{{scrollIntoView && scrollIntoView[level] ? '.scroll-into-view >>> #scroll-to-' + scrollIntoView[level] : '' }}"><t-side-bar wx:if="{{level == 0}}" value="{{innerValue[level]}}" bind:change="onRootChange" t-class="{{classPrefix}}-column {{prefix}}-class-left-column"><t-side-bar-item wx:for="{{treeOptions[level]}}" wx:key="index" label="{{item.label}}" value="{{item.value}}" disabled="{{item.disabled}}" tId="scroll-to-{{item.value}}" class="scroll-into-view" t-class="{{prefix}}-class-left-item"/></t-side-bar><block wx:elif="{{level != leafLevel}}"><view wx:for="{{treeOptions[level]}}" wx:key="value" bind:tap="handleTreeClick" data-level="{{level}}" data-value="{{item.value}}" class="{{_.cls(classPrefix + '__item', [['active', item.value === innerValue[level]], ['disabled', item.disabled]])}} {{prefix}}-class-middle-item scroll-into-view"><view id="scroll-to-{{item.value}}">{{item.label}}</view></view></block><t-radio-group wx:elif="{{!multiple}}" class="{{classPrefix}}__radio {{prefix}}-class-right-column" data-level="{{level}}" data-type="single" value="{{innerValue[level]}}" bind:change="handleChange"><t-radio wx:for="{{treeOptions[level]}}" wx:key="value" tId="scroll-to-{{item.value}}" class="scroll-into-view {{classPrefix}}__radio-item {{prefix}}-class-right-item" t-class-label="{{prefix}}-class-right-item-label" icon="line" value="{{item.value}}" disabled="{{item.disabled}}" maxLabelRow="{{1}}" borderless placement="right">{{item.label}}</t-radio></t-radio-group><t-checkbox-group wx:else class="{{classPrefix}}__checkbox {{prefix}}-class-right-column" value="{{innerValue[level] || []}}" data-level="{{level}}" data-type="multiple" bind:change="handleChange"><t-checkbox wx:for="{{treeOptions[level]}}" wx:key="value" tId="scroll-to-{{item.value}}" class="scroll-into-view {{prefix}}-class-right-item" t-class-label="{{prefix}}-class-right-item-label" placement="right" icon="line" maxLabelRow="{{1}}" value="{{item.value}}" disabled="{{item.disabled}}" borderless>{{item.label}}</t-checkbox></t-checkbox-group></t-scroll-view><slot name="content"/></view>