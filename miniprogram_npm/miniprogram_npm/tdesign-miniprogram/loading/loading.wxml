<wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle, show ? '' : 'display: none', inheritColor ? 'color: inherit' : ''])}}" class="class {{prefix}}-class {{classPrefix}} {{classPrefix + '--' + layout}} {{fullscreen ? classPrefix + '--fullscreen' : ''}}"><view wx:if="{{indicator}}" class="{{prefix}}-class-indicator {{classPrefix}}__spinner {{classPrefix}}__spinner--{{ theme }} {{reverse ? 'reverse' : ''}}" style="width: {{ _.addUnit(size) }}; height: {{ _.addUnit(size) }}; {{inheritColor ? 'color: inherit;' : ''}} {{indicator ? '' : 'display: none;'}} {{duration ? 'animation-duration: ' + duration / 1000 + 's;' : ''}} animation-play-state: {{pause ? 'paused' : 'running'}};" aria-role="{{ariaRole  || 'img'}}" aria-label="{{ ariaLabel || text || '加载中'  }}"><view wx:if="{{ theme === 'spinner' }}" wx:for="{{12}}" wx:key="index" class="{{classPrefix}}__dot {{classPrefix}}__dot-{{index}}"/><view wx:if="{{ theme === 'circular' }}" class="{{classPrefix}}__circular"/><block wx:if="{{ theme === 'dots' }}"><view class="{{classPrefix}}__dot" style="{{duration ? 'animation-duration: ' + duration/1000 + 's; animation-delay:' + 0 + 's;' : ''}} animation-play-state: {{pause ? 'paused' : 'running'}};"></view><view class="{{classPrefix}}__dot" style="{{duration ? 'animation-duration: ' + duration/1000 + 's; animation-delay:' + duration * 1 / 3000 + 's;' : ''}} animation-play-state: {{pause ? 'paused' : 'running'}};"></view><view class="{{classPrefix}}__dot" style="{{duration ? 'animation-duration: ' + duration/1000 + 's; animation-delay:' + duration * 2 / 3000 + 's;' : ''}} animation-play-state: {{pause ? 'paused' : 'running'}};"></view></block><slot name="indicator"/></view><view class="{{_.cls(classPrefix + '__text', [layout])}} {{prefix}}-class-text" aria-hidden="{{indicator}}" aria-label="{{ ariaLabel || text }}"><block wx:if="{{text}}">{{text}}</block><slot name="text"/><slot/></view></view>