const props={actions:{type:Array},buttonLayout:{type:String,value:"horizontal"},cancelBtn:{type:null},closeBtn:{type:null,value:!1},closeOnOverlayClick:{type:Boolean,value:!1},confirmBtn:{type:null},content:{type:String},overlayProps:{type:Object,value:{}},preventScrollThrough:{type:Boolean,value:!0},showOverlay:{type:Boolean,value:!0},title:{type:String},usingCustomNavbar:{type:Boolean,value:!1},visible:{type:Boolean},zIndex:{type:Number,value:11500}};export default props;