{"pages": ["pages/home/<USER>", "pages/login/login", "pages/health/health", "pages/production/production", "pages/shop/shop", "pages/oa/oa", "pages/profile/profile", "pages/profile/settings/settings", "pages/profile/help/help", "pages/more/more"], "usingComponents": {"t-loading": "tdesign-miniprogram/loading/loading", "t-toast": "tdesign-miniprogram/toast/toast"}, "subpackages": [{"root": "subpackages/health", "name": "health", "pages": ["pages/health-records/health-records", "pages/diagnosis/diagnosis", "pages/vaccine/vaccine", "pages/disease/disease", "pages/medicine/medicine", "pages/quarantine/quarantine"]}, {"root": "subpackages/production", "name": "production", "pages": ["pages/environment/environment", "pages/feed/feed", "pages/breeding/breeding", "pages/monitoring/monitoring", "pages/inventory/inventory", "pages/reports/reports"]}, {"root": "subpackages/shop", "name": "shop", "pages": ["pages/products/products"]}, {"root": "subpackages/oa-core", "name": "oa-core", "pages": ["pages/approval/approval", "pages/approval/pending/pending", "pages/approval/history/history", "pages/workflow/workflow", "pages/workflow/templates/templates", "pages/workflow/instances/instances", "pages/notification/notification"]}, {"root": "subpackages/oa-finance", "name": "oa-finance", "pages": ["pages/finance/finance", "pages/finance/overview/overview", "pages/finance/reports/reports", "pages/reimbursement/reimbursement", "pages/reimbursement/list/list", "pages/reimbursement/detail/detail", "pages/reimbursement/apply/apply", "pages/purchase/purchase", "pages/purchase/list/list", "pages/purchase/detail/detail", "pages/purchase/apply/apply"]}, {"root": "subpackages/oa-admin", "name": "oa-admin", "independent": true, "pages": ["pages/permission/users/users", "pages/permission/roles/roles", "pages/leave/leave", "pages/leave/list/list", "pages/leave/detail/detail", "pages/leave/apply/apply", "pages/workflow/designer/designer"]}, {"root": "subpackages/tools", "name": "tools", "independent": true, "pages": ["pages/ai-analysis/ai-analysis", "pages/ai-config/ai-config"]}], "preloadRule": {"pages/health/health": {"packages": ["oa-core"], "network": "all"}, "pages/production/production": {"packages": ["health"], "network": "all"}, "pages/shop/shop": {"packages": ["shop"], "network": "wifi"}, "pages/oa/oa": {"packages": ["oa-core", "oa-finance"], "network": "all"}}, "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#0066CC", "navigationBarTitleText": "智慧养鹅", "navigationBarTextStyle": "white", "backgroundColor": "#f5f5f5", "enablePullDownRefresh": true, "onReachBottomDistance": 50, "backgroundColorTop": "#ffffff", "backgroundColorBottom": "#ffffff", "initialRenderingCache": "static", "visualEffectInBackground": "hidden"}, "tabBar": {"custom": false, "color": "#7A7E83", "selectedColor": "#0066CC", "backgroundColor": "#ffffff", "borderStyle": "white", "list": [{"pagePath": "pages/home/<USER>", "text": "首页", "iconPath": "static/images/icons/home.png", "selectedIconPath": "static/images/icons/home_selected.png"}, {"pagePath": "pages/health/health", "text": "健康", "iconPath": "static/images/icons/health.png", "selectedIconPath": "static/images/icons/health_selected.png"}, {"pagePath": "pages/production/production", "text": "生产", "iconPath": "static/images/icons/production.png", "selectedIconPath": "static/images/icons/production_selected.png"}, {"pagePath": "pages/shop/shop", "text": "商城", "iconPath": "static/images/icons/shop.png", "selectedIconPath": "static/images/icons/shop_selected.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "static/images/icons/profile.png", "selectedIconPath": "static/images/icons/profile_selected.png"}]}, "networkTimeout": {"request": 15000, "downloadFile": 20000, "uploadFile": 20000, "connectSocket": 10000}, "debug": false, "permission": {"scope.userLocation": {"desc": "用于获取您的位置信息，提供基于位置的服务功能"}, "scope.camera": {"desc": "用于访问您的相机，实现拍摄照片和扫描二维码功能"}, "scope.album": {"desc": "用于访问您的相册，实现选择和上传图片功能"}}, "requiredBackgroundModes": ["audio", "location"], "requiredPrivateInfos": ["getLocation"], "plugins": {}, "resizable": true, "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "style": "v2", "componentFramework": "glass-easel", "darkmode": true, "themeLocation": "theme.json", "embeddedAppIdList": [], "halfPage": {"firstPageNavigationStyle": "custom"}}